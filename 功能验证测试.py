#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel导入导出功能验证测试
"""

import pandas as pd
from database import DatabaseManager
from datetime import datetime
import os

def test_export_import_cycle():
    """测试完整的导出-导入循环"""
    print("🔄 开始测试导出-导入循环...")
    
    db = DatabaseManager()
    
    # 1. 确保有测试数据
    original_items = db.get_knowledge_items()
    print(f"📊 原始数据库记录数: {len(original_items)}")
    
    if len(original_items) < 3:
        print("📝 添加测试数据...")
        test_data = [
            (["测试", "导出"], "测试导出功能", "这是一个测试导出功能的条目", "测试用"),
            (["测试", "导入"], "测试导入功能", "这是一个测试导入功能的条目", "测试用"),
            (["功能", "验证"], "功能验证", "验证系统功能是否正常", "重要")
        ]
        
        for tags, title, content, other in test_data:
            db.add_knowledge_item(tags, title, content, other)
        
        original_items = db.get_knowledge_items()
        print(f"✅ 添加测试数据后记录数: {len(original_items)}")
    
    # 2. 测试导出功能
    print("\n📤 测试导出功能...")
    try:
        export_data = []
        for item in original_items:
            created_time = datetime.fromisoformat(item["created_at"]).strftime("%Y-%m-%d %H:%M:%S")
            tags_str = ", ".join(item["tags"]) if item["tags"] else "无标签"
            
            export_data.append({
                "ID": item["id"],
                "标签": tags_str,
                "题目": item["title"],
                "内容": item["content"],
                "其他信息": item["other_info"] if item["other_info"] else "",
                "创建时间": created_time
            })

        df = pd.DataFrame(export_data)
        export_file = f"验证测试导出_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        with pd.ExcelWriter(export_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='知识库数据', index=False)
        
        print(f"✅ 导出成功: {export_file}")
        print(f"📊 导出记录数: {len(export_data)}")
        
    except Exception as e:
        print(f"❌ 导出失败: {str(e)}")
        return False
    
    # 3. 创建新的数据库实例（模拟导入到新系统）
    print("\n📥 测试导入功能...")
    try:
        # 读取刚导出的文件
        import_df = pd.read_excel(export_file, sheet_name='知识库数据')
        print(f"📖 读取导入文件成功，包含 {len(import_df)} 行数据")
        
        # 验证必要列存在
        required_columns = ['题目', '内容']
        missing_columns = [col for col in required_columns if col not in import_df.columns]
        
        if missing_columns:
            print(f"❌ 缺少必要列: {missing_columns}")
            return False
        
        print("✅ 文件格式验证通过")
        
        # 模拟导入过程（不实际导入，避免重复数据）
        import_count = 0
        skip_count = 0
        
        for index, row in import_df.iterrows():
            title = str(row['题目']).strip() if pd.notna(row['题目']) else ""
            content = str(row['内容']).strip() if pd.notna(row['内容']) else ""
            
            if not title or not content:
                skip_count += 1
                continue
            
            # 检查是否重复（在实际导入中会跳过）
            title_exists = any(item['title'] == title for item in original_items)
            if title_exists:
                skip_count += 1
                continue
            
            import_count += 1
        
        print(f"📊 导入分析结果:")
        print(f"   - 可导入: {import_count} 条")
        print(f"   - 会跳过: {skip_count} 条 (重复或空数据)")
        
    except Exception as e:
        print(f"❌ 导入测试失败: {str(e)}")
        return False
    
    # 4. 清理测试文件
    try:
        os.remove(export_file)
        print(f"🗑️ 清理测试文件: {export_file}")
    except:
        pass
    
    print("\n✅ 导出-导入循环测试完成！")
    return True

def test_import_format_validation():
    """测试导入格式验证"""
    print("\n🔍 测试导入格式验证...")
    
    # 创建各种格式的测试文件
    test_cases = [
        {
            "name": "正确格式",
            "data": [
                {"标签": "测试", "题目": "正确格式测试", "内容": "这是正确格式的测试数据", "其他信息": "测试"},
            ],
            "should_pass": True
        },
        {
            "name": "缺少内容列",
            "data": [
                {"标签": "测试", "题目": "缺少内容测试"},
            ],
            "should_pass": False
        },
        {
            "name": "缺少题目列",
            "data": [
                {"标签": "测试", "内容": "缺少题目测试", "其他信息": "测试"},
            ],
            "should_pass": False
        },
        {
            "name": "只有必需列",
            "data": [
                {"题目": "最简格式测试", "内容": "只包含必需列的测试数据"},
            ],
            "should_pass": True
        }
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\n📋 测试用例 {i+1}: {test_case['name']}")
        
        try:
            df = pd.DataFrame(test_case['data'])
            test_file = f"格式测试_{i+1}_{datetime.now().strftime('%H%M%S')}.xlsx"
            df.to_excel(test_file, index=False)
            
            # 验证格式
            required_columns = ['题目', '内容']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                result = "❌ 格式验证失败"
                expected = "应该失败" if not test_case['should_pass'] else "应该通过"
            else:
                result = "✅ 格式验证通过"
                expected = "应该通过" if test_case['should_pass'] else "应该失败"
            
            print(f"   {result} ({expected})")
            
            # 清理测试文件
            try:
                os.remove(test_file)
            except:
                pass
                
        except Exception as e:
            print(f"   ❌ 测试失败: {str(e)}")
    
    print("\n✅ 格式验证测试完成！")

def show_feature_summary():
    """显示功能总结"""
    print("\n" + "="*60)
    print("🎯 Excel导入导出功能总结")
    print("="*60)
    
    print("\n📤 导出功能:")
    print("   ✅ 支持全量导出和筛选导出")
    print("   ✅ 多工作表结构（数据、统计、标签统计）")
    print("   ✅ 自动格式化和列宽调整")
    print("   ✅ 错误处理和用户友好提示")
    
    print("\n📥 导入功能:")
    print("   ✅ 智能列识别和格式验证")
    print("   ✅ 导入预览和确认机制")
    print("   ✅ 重复数据检测和跳过")
    print("   ✅ 详细的导入结果报告")
    print("   ✅ 支持多种标签分隔符")
    
    print("\n🛠️ 技术特性:")
    print("   ✅ 基于pandas和openpyxl")
    print("   ✅ 完整的错误处理")
    print("   ✅ 用户友好的界面")
    print("   ✅ 数据安全保护")
    
    print("\n💡 使用建议:")
    print("   📋 定期导出数据作为备份")
    print("   📋 导入前先用小量数据测试")
    print("   📋 保持Excel文件格式规范")
    print("   📋 使用一致的标签命名")

if __name__ == "__main__":
    print("🚀 开始Excel导入导出功能验证测试")
    print("="*60)
    
    # 测试导出-导入循环
    success1 = test_export_import_cycle()
    
    # 测试格式验证
    test_import_format_validation()
    
    # 显示功能总结
    show_feature_summary()
    
    print("\n🎉 所有测试完成！")
    if success1:
        print("✅ 导入导出功能正常工作")
    else:
        print("❌ 部分功能存在问题，请检查")
    
    print("\n💻 现在可以启动主程序测试完整功能:")
    print("   python main.py")

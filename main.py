import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk, filedialog
from database import DatabaseManager
from visualization import VisualizationManager
import random
from datetime import datetime
import pandas as pd
import os

class KnowledgeBaseApp:
    def __init__(self):
        # 设置主题
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")

        # 初始化数据库和可视化管理器
        self.db = DatabaseManager()
        self.viz = VisualizationManager()

        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title("智能知识管理系统")
        self.root.geometry("1200x800")

        # 创建界面
        self.create_widgets()
        self.refresh_tags()
        self.refresh_items_list()

    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 左侧输入区域 - 添加滚动功能
        left_frame = ctk.CTkScrollableFrame(main_frame, width=300)
        left_frame.pack(side="left", fill="y", padx=(0, 5))

        # 输入区域标题
        input_title = ctk.CTkLabel(left_frame, text="知识输入", font=ctk.CTkFont(size=18, weight="bold"))
        input_title.pack(pady=(10, 15))

        # 标签管理
        ctk.CTkLabel(left_frame, text="标签管理:", font=ctk.CTkFont(size=14)).pack(anchor="w", padx=15)

        # 标签输入框架
        tag_input_frame = ctk.CTkFrame(left_frame)
        tag_input_frame.pack(pady=(3, 5), padx=15, fill="x")

        # 标签输入框（支持自动匹配）
        self.tag_entry = ctk.CTkEntry(tag_input_frame, width=180, placeholder_text="输入标签名称...")
        self.tag_entry.pack(side="left", padx=(5, 5))
        self.tag_entry.bind("<KeyRelease>", self.on_tag_input_change)
        self.tag_entry.bind("<Return>", self.add_tag_from_entry)

        # 添加标签按钮
        add_tag_btn = ctk.CTkButton(tag_input_frame, text="添加", width=50, command=self.add_tag_from_entry)
        add_tag_btn.pack(side="left", padx=2)

        # 推荐标签显示框
        self.suggestions_frame = ctk.CTkFrame(left_frame)
        self.suggestions_label = ctk.CTkLabel(self.suggestions_frame, text="", font=ctk.CTkFont(size=10))
        self.suggestions_label.pack(padx=5, pady=2)

        # 已选标签显示
        self.selected_tags_frame = ctk.CTkFrame(left_frame)
        self.selected_tags_frame.pack(pady=(5, 10), padx=15, fill="x")

        self.selected_tags_label = ctk.CTkLabel(self.selected_tags_frame, text="已选标签: 无",
                                              font=ctk.CTkFont(size=11))
        self.selected_tags_label.pack(pady=3)

        # 存储已选标签
        self.selected_tags = []

        # 常用标签管理
        tags_management_frame = ctk.CTkFrame(left_frame)
        tags_management_frame.pack(pady=(0, 10), padx=15, fill="both", expand=True)

        # 标题和删除按钮
        tags_header = ctk.CTkFrame(tags_management_frame)
        tags_header.pack(fill="x", padx=5, pady=(5, 0))

        ctk.CTkLabel(tags_header, text="常用标签:", font=ctk.CTkFont(size=12)).pack(side="left", padx=5)

        delete_tag_btn = ctk.CTkButton(tags_header, text="删除标签", width=80, height=25,
                                     fg_color="red", hover_color="darkred",
                                     command=self.toggle_delete_mode)
        delete_tag_btn.pack(side="right", padx=5)

        # 常用标签滚动列表（固定滚动焦点）
        self.tags_scrollable = ctk.CTkScrollableFrame(tags_management_frame, height=100)
        self.tags_scrollable.pack(fill="both", expand=True, padx=5, pady=5)

        # 绑定鼠标事件，确保滚动焦点
        self.tags_scrollable.bind("<Enter>", lambda e: self.tags_scrollable.focus_set())
        self.tags_scrollable.bind("<Leave>", lambda e: self.focus_set())

        # 删除模式标志
        self.delete_mode = False

        # 题目输入
        ctk.CTkLabel(left_frame, text="题目:", font=ctk.CTkFont(size=14)).pack(anchor="w", padx=15)
        self.title_entry = ctk.CTkEntry(left_frame, width=250, height=32)
        self.title_entry.pack(pady=(3, 10), padx=15)

        # 内容输入
        ctk.CTkLabel(left_frame, text="内容:", font=ctk.CTkFont(size=14)).pack(anchor="w", padx=15)
        self.content_text = ctk.CTkTextbox(left_frame, width=250, height=120)
        self.content_text.pack(pady=(3, 10), padx=15)

        # 其他信息输入
        ctk.CTkLabel(left_frame, text="其他信息:", font=ctk.CTkFont(size=14)).pack(anchor="w", padx=15)
        self.other_text = ctk.CTkTextbox(left_frame, width=250, height=60)
        self.other_text.pack(pady=(3, 10), padx=15)

        # 保存按钮
        save_btn = ctk.CTkButton(left_frame, text="保存知识", command=self.save_knowledge,
                                font=ctk.CTkFont(size=14, weight="bold"), height=35)
        save_btn.pack(pady=15, padx=15)

        # 右侧主要功能区域
        right_frame = ctk.CTkFrame(main_frame)
        right_frame.pack(side="right", fill="both", expand=True, padx=(5, 0))

        # 创建选项卡
        self.notebook = ctk.CTkTabview(right_frame)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # 知识列表选项卡
        self.create_list_tab()

        # 可视化选项卡
        self.create_visualization_tab()

        # 练习选项卡
        self.create_practice_tab()

        # 搜索选项卡
        self.create_search_tab()

    def create_list_tab(self):
        """创建知识列表选项卡"""
        list_tab = self.notebook.add("知识列表")

        # 简化的控制框架
        control_frame = ctk.CTkFrame(list_tab)
        control_frame.pack(fill="x", padx=10, pady=(10, 5))

        # 标签筛选区域
        filter_section = ctk.CTkFrame(control_frame)
        filter_section.pack(side="left", padx=10, fill="y")

        # 筛选标题
        ctk.CTkLabel(filter_section, text="筛选:", font=ctk.CTkFont(size=12)).pack(anchor="w", padx=5, pady=(5, 0))

        # 筛选输入框
        filter_input_frame = ctk.CTkFrame(filter_section)
        filter_input_frame.pack(fill="x", padx=5, pady=2)

        self.filter_entry = ctk.CTkEntry(filter_input_frame, placeholder_text="输入标签筛选...", width=120)
        self.filter_entry.pack(side="left", padx=2)
        self.filter_entry.bind("<KeyRelease>", self.on_filter_input_change)
        self.filter_entry.bind("<Return>", self.add_filter_tag_from_entry)

        add_filter_btn = ctk.CTkButton(filter_input_frame, text="添加", width=40, height=28,
                                     command=self.add_filter_tag_from_entry)
        add_filter_btn.pack(side="right", padx=2)

        # 筛选标签显示区域
        self.filter_tags_frame = ctk.CTkFrame(filter_section)
        self.filter_tags_frame.pack(fill="x", padx=5, pady=2)

        # 筛选建议框（初始隐藏）
        self.filter_suggestions_frame = ctk.CTkFrame(filter_section)

        # 清除筛选按钮
        clear_filter_btn = ctk.CTkButton(filter_section, text="清除筛选", width=80, height=25,
                                       fg_color="gray", hover_color="darkgray",
                                       command=self.clear_all_filters)
        clear_filter_btn.pack(pady=2)

        # 多标签筛选
        self.multi_filter_tags = []

        # 操作按钮
        refresh_btn = ctk.CTkButton(control_frame, text="刷新", command=self.refresh_items_list, width=80)
        refresh_btn.pack(side="left", padx=(20, 5))

        delete_btn = ctk.CTkButton(control_frame, text="删除选中", command=self.delete_selected_item,
                                 width=80, fg_color="red", hover_color="darkred")
        delete_btn.pack(side="left", padx=5)

        export_btn = ctk.CTkButton(control_frame, text="导出Excel", command=self.export_to_excel,
                                 width=80, fg_color="green", hover_color="darkgreen")
        export_btn.pack(side="left", padx=5)

        import_btn = ctk.CTkButton(control_frame, text="导入Excel", command=self.import_from_excel,
                                 width=80, fg_color="blue", hover_color="darkblue")
        import_btn.pack(side="left", padx=5)

        # 初始化排序状态
        self.current_sort_column = "id"
        self.sort_ascending = True

        # 初始化列显示状态
        self.show_id_var = tk.BooleanVar(value=True)
        self.show_tags_var = tk.BooleanVar(value=True)
        self.show_title_var = tk.BooleanVar(value=True)
        self.show_time_var = tk.BooleanVar(value=True)

        # 知识列表
        list_frame = ctk.CTkFrame(list_tab)
        list_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 创建Treeview容器
        self.tree_container = list_frame
        self.create_treeview()

        # 绑定双击事件
        self.items_tree.bind("<Double-1>", self.show_item_detail)

    def create_treeview(self):
        """创建Treeview"""
        # 清除现有的treeview
        for widget in self.tree_container.winfo_children():
            widget.destroy()

        # 根据选择的列创建columns
        columns = []
        column_mapping = {}  # 显示名称到内部名称的映射

        if self.show_id_var.get():
            columns.append("ID")
            column_mapping["ID"] = "id"
        if self.show_tags_var.get():
            columns.append("标签")
            column_mapping["标签"] = "tags"
        if self.show_title_var.get():
            columns.append("题目")
            column_mapping["题目"] = "title"
        if self.show_time_var.get():
            columns.append("创建时间")
            column_mapping["创建时间"] = "created_at"

        # 创建新的Treeview
        self.items_tree = ttk.Treeview(self.tree_container, columns=columns, show="headings", height=15)

        # 设置列标题和宽度，绑定点击事件
        for col in columns:
            # 设置列标题，添加排序指示器
            sort_indicator = ""
            if column_mapping[col] == self.current_sort_column:
                sort_indicator = " ↑" if self.sort_ascending else " ↓"

            self.items_tree.heading(col, text=col + sort_indicator,
                                  command=lambda c=col: self.on_column_click(column_mapping[c]))

            # 设置列宽
            if col == "ID":
                self.items_tree.column(col, width=80)
            elif col == "标签":
                self.items_tree.column(col, width=120)
            elif col == "题目":
                self.items_tree.column(col, width=200)
            elif col == "创建时间":
                self.items_tree.column(col, width=150)

        # 滚动条
        scrollbar = ttk.Scrollbar(self.tree_container, orient="vertical", command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=scrollbar.set)

        self.items_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定右键菜单
        self.items_tree.bind("<Button-3>", self.show_context_menu)

    def on_column_click(self, column):
        """处理列标题点击事件"""
        if self.current_sort_column == column:
            # 如果点击的是当前排序列，则切换排序方向
            self.sort_ascending = not self.sort_ascending
        else:
            # 如果点击的是新列，则设置为升序
            self.current_sort_column = column
            self.sort_ascending = True

        # 重新创建treeview以更新排序指示器
        self.create_treeview()
        # 刷新数据
        self.refresh_items_list()

    def show_context_menu(self, event):
        """显示右键菜单"""
        # 创建右键菜单
        context_menu = tk.Menu(self.root, tearoff=0)

        # 添加列显示选项
        context_menu.add_separator()
        context_menu.add_checkbutton(label="显示ID列", variable=self.show_id_var,
                                   command=self.update_columns)
        context_menu.add_checkbutton(label="显示标签列", variable=self.show_tags_var,
                                   command=self.update_columns)
        context_menu.add_checkbutton(label="显示题目列", variable=self.show_title_var,
                                   command=self.update_columns)
        context_menu.add_checkbutton(label="显示时间列", variable=self.show_time_var,
                                   command=self.update_columns)

        # 显示菜单
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def update_columns(self):
        """更新显示的列"""
        self.create_treeview()
        self.refresh_items_list()

    def create_visualization_tab(self):
        """创建可视化选项卡"""
        viz_tab = self.notebook.add("可视化")

        # 控制按钮
        control_frame = ctk.CTkFrame(viz_tab)
        control_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkButton(control_frame, text="网点图", command=self.show_network_graph).pack(side="left", padx=5)
        ctk.CTkButton(control_frame, text="思维导图", command=self.show_mind_map).pack(side="left", padx=5)
        ctk.CTkButton(control_frame, text="分类分布", command=self.show_distribution).pack(side="left", padx=5)

        # 可视化显示区域
        self.viz_frame = ctk.CTkFrame(viz_tab)
        self.viz_frame.pack(fill="both", expand=True, padx=10, pady=10)

    def create_practice_tab(self):
        """创建练习选项卡"""
        practice_tab = self.notebook.add("练习模式")

        # 控制区域
        control_frame = ctk.CTkFrame(practice_tab)
        control_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkButton(control_frame, text="随机题目", command=self.show_random_question).pack(side="left", padx=5)
        ctk.CTkButton(control_frame, text="复习提醒", command=self.show_review_items).pack(side="left", padx=5)

        # 题目显示区域
        self.question_frame = ctk.CTkFrame(practice_tab)
        self.question_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 初始化显示
        welcome_label = ctk.CTkLabel(self.question_frame, text="点击'随机题目'开始练习",
                                   font=ctk.CTkFont(size=16))
        welcome_label.pack(expand=True)

    def create_search_tab(self):
        """创建搜索选项卡"""
        search_tab = self.notebook.add("搜索")

        # 搜索控制区域
        search_control = ctk.CTkFrame(search_tab)
        search_control.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(search_control, text="搜索关键词:").pack(side="left", padx=(10, 5))
        self.search_entry = ctk.CTkEntry(search_control, width=300)
        self.search_entry.pack(side="left", padx=5)

        search_btn = ctk.CTkButton(search_control, text="搜索", command=self.search_knowledge)
        search_btn.pack(side="left", padx=5)

        # 搜索结果显示
        self.search_frame = ctk.CTkFrame(search_tab)
        self.search_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 搜索结果列表
        search_columns = ("ID", "标签", "题目", "匹配内容")
        self.search_tree = ttk.Treeview(self.search_frame, columns=search_columns, show="headings", height=20)

        for col in search_columns:
            self.search_tree.heading(col, text=col)
            self.search_tree.column(col, width=200)

        search_scrollbar = ttk.Scrollbar(self.search_frame, orient="vertical", command=self.search_tree.yview)
        self.search_tree.configure(yscrollcommand=search_scrollbar.set)

        self.search_tree.pack(side="left", fill="both", expand=True)
        search_scrollbar.pack(side="right", fill="y")

        self.search_tree.bind("<Double-1>", self.show_search_item_detail)



    def remove_tag(self, tag_name):
        """移除已选标签（向后兼容）"""
        self.remove_selected_tag(tag_name)

    def save_knowledge(self):
        """保存知识"""
        tags = self.selected_tags.copy()
        title = self.title_entry.get().strip()
        content = self.content_text.get("1.0", "end-1c").strip()
        other = self.other_text.get("1.0", "end-1c").strip()

        if not tags or not title or not content:
            messagebox.showerror("错误", "标签、题目和内容不能为空！")
            return

        try:
            # 确保tags是列表格式
            if isinstance(tags, str):
                tags = [tags]
            self.db.add_knowledge_item(tags, title, content, other)
            messagebox.showinfo("成功", "知识保存成功！")

            # 清空输入框
            self.title_entry.delete(0, "end")
            self.content_text.delete("1.0", "end")
            self.other_text.delete("1.0", "end")
            self.selected_tags.clear()
            self.update_selected_tags_display()

            # 刷新界面
            self.refresh_tags()
            self.refresh_items_list()

        except Exception as e:
            messagebox.showerror("错误", f"保存失败：{str(e)}")

    def refresh_tags(self):
        """刷新标签列表"""
        # 筛选选项会在显示下拉框时动态获取，无需在这里更新

        # 刷新标签显示列表
        self.refresh_tags_display()

    def on_tag_input_change(self, event):
        """标签输入变化时的处理"""
        input_text = self.tag_entry.get().strip()
        if len(input_text) >= 1:
            # 获取匹配的标签
            all_tags = self.db.get_tags()
            matching_tags = [tag["name"] for tag in all_tags
                           if input_text.lower() in tag["name"].lower()]

            if matching_tags:
                # 显示推荐
                suggestions_text = "推荐: " + ", ".join(matching_tags[:5])  # 最多显示5个
                self.suggestions_label.configure(text=suggestions_text)
                self.suggestions_frame.pack(pady=(0, 5), padx=15, fill="x")
            else:
                self.suggestions_frame.pack_forget()
        else:
            self.suggestions_frame.pack_forget()

    def add_tag_from_entry(self, event=None):
        """从输入框添加标签"""
        tag_name = self.tag_entry.get().strip()
        if tag_name and tag_name not in self.selected_tags:
            self.selected_tags.append(tag_name)
            self.update_selected_tags_display()
            self.tag_entry.delete(0, tk.END)
            self.suggestions_frame.pack_forget()
            self.refresh_tags_display()

    def remove_selected_tag(self, tag_name):
        """移除选中的标签"""
        if tag_name in self.selected_tags:
            self.selected_tags.remove(tag_name)
            self.update_selected_tags_display()
            self.refresh_tags_display()

    def update_selected_tags_display(self):
        """更新已选标签显示"""
        # 清除现有显示
        for widget in self.selected_tags_frame.winfo_children():
            if widget != self.selected_tags_label:
                widget.destroy()

        if self.selected_tags:
            self.selected_tags_label.configure(text="已选标签:")

            # 创建标签按钮框架
            tags_display_frame = ctk.CTkFrame(self.selected_tags_frame)
            tags_display_frame.pack(pady=2, fill="x", padx=3)

            # 分行显示标签，每行最多3个
            current_row = None
            for i, tag in enumerate(self.selected_tags):
                if i % 3 == 0:  # 每3个标签换一行
                    current_row = ctk.CTkFrame(tags_display_frame)
                    current_row.pack(fill="x", pady=1)

                tag_btn = ctk.CTkButton(current_row, text=f"{tag} ×",
                                      command=lambda t=tag: self.remove_tag(t),
                                      width=70, height=22, font=ctk.CTkFont(size=9))
                tag_btn.pack(side="left", padx=1, pady=1)
        else:
            self.selected_tags_label.configure(text="已选标签: 无")

    def refresh_tags_display(self):
        """刷新标签显示列表"""
        # 清除现有标签按钮
        for widget in self.tags_scrollable.winfo_children():
            widget.destroy()

        # 获取所有标签
        all_tags = self.db.get_tags()

        # 创建标签按钮
        for tag in all_tags:
            tag_name = tag["name"]

            # 创建标签框架
            tag_frame = ctk.CTkFrame(self.tags_scrollable)
            tag_frame.pack(fill="x", padx=2, pady=1)

            # 标签名称
            tag_label = ctk.CTkLabel(tag_frame, text=tag_name, width=120)
            tag_label.pack(side="left", padx=5, pady=2)

            if self.delete_mode:
                # 删除模式：显示删除按钮
                delete_btn = ctk.CTkButton(tag_frame, text="删除", width=40, height=20,
                                         fg_color="red", hover_color="darkred",
                                         command=lambda t=tag_name: self.delete_tag_from_db(t))
                delete_btn.pack(side="right", padx=5, pady=2)
            else:
                # 正常模式：显示添加/移除按钮
                if tag_name not in self.selected_tags:
                    add_btn = ctk.CTkButton(tag_frame, text="添加", width=40, height=20,
                                          command=lambda t=tag_name: self.add_existing_tag(t))
                    add_btn.pack(side="right", padx=5, pady=2)
                else:
                    remove_btn = ctk.CTkButton(tag_frame, text="移除", width=40, height=20,
                                             fg_color="orange", hover_color="darkorange",
                                             command=lambda t=tag_name: self.remove_selected_tag(t))
                    remove_btn.pack(side="right", padx=5, pady=2)

    def add_existing_tag(self, tag_name):
        """添加现有标签"""
        if tag_name not in self.selected_tags:
            self.selected_tags.append(tag_name)
            self.update_selected_tags_display()
            self.refresh_tags_display()

    def toggle_delete_mode(self):
        """切换删除模式"""
        self.delete_mode = not self.delete_mode
        self.refresh_tags_display()

    def delete_tag_from_db(self, tag_name):
        """从数据库删除标签"""
        if messagebox.askyesno("确认删除", f"确定要删除标签 '{tag_name}' 吗？\n这将删除所有使用此标签的关联。"):
            try:
                # 获取标签ID
                tags = self.db.get_tags()
                tag_id = None
                for tag in tags:
                    if tag["name"] == tag_name:
                        tag_id = tag["id"]
                        break

                if tag_id:
                    # 删除标签及其关联
                    self.db.delete_tag(tag_id)

                    # 如果该标签在已选标签中，也要移除
                    if tag_name in self.selected_tags:
                        self.selected_tags.remove(tag_name)
                        self.update_selected_tags_display()

                    # 刷新显示
                    self.refresh_tags()
                    self.refresh_items_list()

                    messagebox.showinfo("成功", f"标签 '{tag_name}' 已删除")

            except Exception as e:
                messagebox.showerror("错误", f"删除标签失败：{str(e)}")

    def on_filter_input_change(self, event):
        """筛选输入变化时的处理"""
        input_text = self.filter_entry.get().strip()
        if len(input_text) >= 1:
            # 显示匹配的标签建议
            self.show_filter_suggestions(input_text)
        else:
            self.filter_suggestions_frame.pack_forget()

    def show_filter_suggestions(self, input_text):
        """显示筛选标签建议"""
        # 清空现有建议
        for widget in self.filter_suggestions_frame.winfo_children():
            widget.destroy()

        # 获取匹配的标签
        all_tags = self.db.get_tags()
        matching_tags = [tag["name"] for tag in all_tags
                        if input_text.lower() in tag["name"].lower()
                        and tag["name"] not in self.multi_filter_tags]

        if matching_tags:
            # 显示建议框
            self.filter_suggestions_frame.pack(fill="x", padx=5, pady=2)

            # 最多显示5个建议
            for tag_name in matching_tags[:5]:
                suggestion_btn = ctk.CTkButton(self.filter_suggestions_frame, text=tag_name,
                                             width=100, height=20, font=ctk.CTkFont(size=10),
                                             command=lambda t=tag_name: self.add_filter_tag_from_suggestion(t))
                suggestion_btn.pack(side="left", padx=2, pady=1)
        else:
            self.filter_suggestions_frame.pack_forget()

    def add_filter_tag_from_suggestion(self, tag_name):
        """从建议中添加筛选标签"""
        if tag_name not in self.multi_filter_tags:
            self.multi_filter_tags.append(tag_name)
            self.filter_entry.delete(0, tk.END)
            self.filter_suggestions_frame.pack_forget()
            self.update_filter_tags_display()
            self.refresh_items_list()

    def add_filter_tag_from_entry(self, event=None):
        """从输入框添加筛选标签"""
        tag_name = self.filter_entry.get().strip()
        if tag_name and tag_name not in self.multi_filter_tags:
            # 检查标签是否存在
            all_tags = self.db.get_tags()
            tag_exists = any(tag["name"] == tag_name for tag in all_tags)

            if tag_exists:
                self.multi_filter_tags.append(tag_name)
                self.filter_entry.delete(0, tk.END)
                self.filter_suggestions_frame.pack_forget()
                self.update_filter_tags_display()
                self.refresh_items_list()
            else:
                messagebox.showwarning("标签不存在", f"标签 '{tag_name}' 不存在")

    def update_filter_tags_display(self):
        """更新筛选标签显示"""
        # 清空现有显示
        for widget in self.filter_tags_frame.winfo_children():
            widget.destroy()

        # 显示当前筛选标签
        for tag_name in self.multi_filter_tags:
            tag_frame = ctk.CTkFrame(self.filter_tags_frame)
            tag_frame.pack(side="left", padx=2, pady=1)

            tag_label = ctk.CTkLabel(tag_frame, text=tag_name, font=ctk.CTkFont(size=10))
            tag_label.pack(side="left", padx=5, pady=2)

            remove_btn = ctk.CTkButton(tag_frame, text="×", width=20, height=20,
                                     fg_color="red", hover_color="darkred",
                                     command=lambda t=tag_name: self.remove_filter_tag(t))
            remove_btn.pack(side="right", padx=2, pady=2)

    def remove_filter_tag(self, tag_name):
        """移除筛选标签"""
        if tag_name in self.multi_filter_tags:
            self.multi_filter_tags.remove(tag_name)
            self.update_filter_tags_display()
            self.refresh_items_list()

    def clear_all_filters(self):
        """清除所有筛选"""
        self.multi_filter_tags = []
        self.filter_entry.delete(0, tk.END)
        self.filter_suggestions_frame.pack_forget()
        self.update_filter_tags_display()
        self.refresh_items_list()



    # 保持向后兼容性
    def refresh_categories(self):
        """刷新分类列表（向后兼容）"""
        self.refresh_tags()

    def refresh_items_list(self):
        """刷新知识列表"""
        # 清空现有项目
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # 获取排序方式和方向
        order_by = self.current_sort_column
        desc = not self.sort_ascending  # 注意：数据库的desc参数与UI的ascending相反

        # 使用多标签筛选（如果没有筛选标签则显示全部）
        if hasattr(self, 'multi_filter_tags') and self.multi_filter_tags:
            # 多标签筛选
            items = self.get_items_by_multi_tags(self.multi_filter_tags, order_by, desc)
        else:
            # 显示全部项目
            items = self.db.get_knowledge_items(None, order_by, desc)

        # 添加到列表（根据选择的列）
        for item in items:
            values = []

            if self.show_id_var.get():
                values.append(item["id"])
            if self.show_tags_var.get():
                tags_display = ", ".join(item["tags"]) if item["tags"] else "无标签"
                values.append(tags_display)
            if self.show_title_var.get():
                values.append(item["title"])
            if self.show_time_var.get():
                created_at = datetime.fromisoformat(item["created_at"]).strftime("%Y-%m-%d %H:%M")
                values.append(created_at)

            self.items_tree.insert("", "end", values=tuple(values))

    def get_items_by_multi_tags(self, tag_names, order_by="created_at", desc=False):
        """根据多个标签筛选项目"""
        if not tag_names:
            return self.db.get_knowledge_items(None, order_by, desc)

        # 获取所有项目
        all_items = self.db.get_knowledge_items(None, order_by, desc)

        # 筛选包含所有指定标签的项目
        filtered_items = []
        for item in all_items:
            item_tags = set(item["tags"]) if item["tags"] else set()
            required_tags = set(tag_names)

            # 检查项目是否包含所有要求的标签
            if required_tags.issubset(item_tags):
                filtered_items.append(item)

        return filtered_items

    def show_item_detail(self, event):
        """显示知识详情"""
        selection = self.items_tree.selection()
        if not selection:
            return

        item_data = self.items_tree.item(selection[0])["values"]
        item_id = item_data[0]

        # 获取完整信息
        item_detail = self.db.get_item_by_id(item_id)

        if item_detail:
            self.show_detail_window(item_detail)

    def show_detail_window(self, item):
        """显示详情窗口"""
        detail_window = ctk.CTkToplevel(self.root)
        detail_window.title(f"知识详情 - {item['title']}")
        detail_window.geometry("600x500")

        # 标题
        title_label = ctk.CTkLabel(detail_window, text=item["title"],
                                 font=ctk.CTkFont(size=18, weight="bold"))
        title_label.pack(pady=10)

        # 标签和时间
        info_frame = ctk.CTkFrame(detail_window)
        info_frame.pack(fill="x", padx=20, pady=5)

        tags_display = ", ".join(item['tags']) if item['tags'] else "无标签"
        ctk.CTkLabel(info_frame, text=f"标签: {tags_display}").pack(side="left", padx=10)
        created_time = datetime.fromisoformat(item["created_at"]).strftime("%Y-%m-%d %H:%M")
        ctk.CTkLabel(info_frame, text=f"创建时间: {created_time}").pack(side="right", padx=10)

        # 内容
        ctk.CTkLabel(detail_window, text="内容:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=20, pady=(20, 5))
        content_text = ctk.CTkTextbox(detail_window, height=200)
        content_text.pack(fill="both", expand=True, padx=20, pady=5)
        content_text.insert("1.0", item["content"])
        content_text.configure(state="disabled")

        # 其他信息
        if item["other_info"]:
            ctk.CTkLabel(detail_window, text="其他信息:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=20, pady=(10, 5))
            other_text = ctk.CTkTextbox(detail_window, height=80)
            other_text.pack(fill="x", padx=20, pady=5)
            other_text.insert("1.0", item["other_info"])
            other_text.configure(state="disabled")

        # 操作按钮
        button_frame = ctk.CTkFrame(detail_window)
        button_frame.pack(fill="x", padx=20, pady=10)

        delete_btn = ctk.CTkButton(button_frame, text="删除此项",
                                 command=lambda: self.delete_item_from_detail(item["id"], detail_window),
                                 fg_color="red", hover_color="darkred", width=100)
        delete_btn.pack(side="right", padx=10)

    def show_network_graph(self):
        """显示网点图"""
        self.clear_viz_frame()
        items = self.db.get_knowledge_items()
        if items:
            canvas = self.viz.create_network_graph(items, self.viz_frame)
            canvas.get_tk_widget().pack(fill="both", expand=True)
        else:
            ctk.CTkLabel(self.viz_frame, text="暂无数据").pack(expand=True)

    def show_mind_map(self):
        """显示思维导图"""
        self.clear_viz_frame()
        items = self.db.get_knowledge_items()
        if items:
            canvas = self.viz.create_mind_map(items, self.viz_frame)
            canvas.get_tk_widget().pack(fill="both", expand=True)
        else:
            ctk.CTkLabel(self.viz_frame, text="暂无数据").pack(expand=True)

    def show_distribution(self):
        """显示分类分布"""
        self.clear_viz_frame()
        items = self.db.get_knowledge_items()
        if items:
            canvas = self.viz.show_category_distribution(items, self.viz_frame)
            canvas.get_tk_widget().pack(fill="both", expand=True)
        else:
            ctk.CTkLabel(self.viz_frame, text="暂无数据").pack(expand=True)

    def clear_viz_frame(self):
        """清空可视化框架"""
        for widget in self.viz_frame.winfo_children():
            widget.destroy()

    def show_random_question(self):
        """显示随机题目"""
        self.clear_question_frame()

        item = self.db.get_random_item()
        if not item:
            ctk.CTkLabel(self.question_frame, text="暂无题目", font=ctk.CTkFont(size=16)).pack(expand=True)
            return

        # 题目显示
        question_label = ctk.CTkLabel(self.question_frame, text=f"题目: {item['title']}",
                                    font=ctk.CTkFont(size=18, weight="bold"))
        question_label.pack(pady=20)

        tags_display = ", ".join(item['tags']) if item['tags'] else "无标签"
        tags_label = ctk.CTkLabel(self.question_frame, text=f"标签: {tags_display}",
                                font=ctk.CTkFont(size=14))
        tags_label.pack(pady=5)

        # 按钮框架
        button_frame = ctk.CTkFrame(self.question_frame)
        button_frame.pack(pady=20)

        # 显示答案按钮
        show_answer_btn = ctk.CTkButton(button_frame, text="显示答案",
                                      command=lambda: self.show_answer(item))
        show_answer_btn.pack(side="left", padx=5)

        # 学习状态按钮
        status_frame = ctk.CTkFrame(self.question_frame)
        status_frame.pack(pady=10)

        ctk.CTkLabel(status_frame, text="学习状态:", font=ctk.CTkFont(size=14)).pack()

        btn_frame = ctk.CTkFrame(status_frame)
        btn_frame.pack(pady=10)

        ctk.CTkButton(btn_frame, text="不会", fg_color="red",
                     command=lambda: self.update_status(item['id'], 0)).pack(side="left", padx=5)
        ctk.CTkButton(btn_frame, text="模糊", fg_color="orange",
                     command=lambda: self.update_status(item['id'], 1)).pack(side="left", padx=5)
        ctk.CTkButton(btn_frame, text="已学会", fg_color="green",
                     command=lambda: self.update_status(item['id'], 2)).pack(side="left", padx=5)

    def show_answer(self, item):
        """显示答案"""
        answer_window = ctk.CTkToplevel(self.root)
        answer_window.title("答案")
        answer_window.geometry("500x400")

        # 题目
        ctk.CTkLabel(answer_window, text=f"题目: {item['title']}",
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 答案内容
        ctk.CTkLabel(answer_window, text="答案:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=20)
        answer_text = ctk.CTkTextbox(answer_window, height=200)
        answer_text.pack(fill="both", expand=True, padx=20, pady=10)
        answer_text.insert("1.0", item["content"])
        answer_text.configure(state="disabled")

        # 其他信息
        if item["other_info"]:
            ctk.CTkLabel(answer_window, text="其他信息:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=20)
            other_text = ctk.CTkTextbox(answer_window, height=80)
            other_text.pack(fill="x", padx=20, pady=10)
            other_text.insert("1.0", item["other_info"])
            other_text.configure(state="disabled")

    def update_status(self, item_id, status):
        """更新学习状态"""
        self.db.update_learning_status(item_id, status)
        status_names = {0: "不会", 1: "模糊", 2: "已学会"}
        messagebox.showinfo("状态更新", f"已标记为: {status_names[status]}")

    def clear_question_frame(self):
        """清空题目框架"""
        for widget in self.question_frame.winfo_children():
            widget.destroy()

    def show_review_items(self):
        """显示需要复习的项目"""
        review_items = self.db.get_items_for_review()

        if not review_items:
            messagebox.showinfo("复习提醒", "暂无需要复习的内容")
            return

        review_window = ctk.CTkToplevel(self.root)
        review_window.title("复习提醒")
        review_window.geometry("800x600")

        ctk.CTkLabel(review_window, text=f"需要复习的内容 ({len(review_items)}项)",
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 复习列表
        review_columns = ("题目", "标签", "状态")
        review_tree = ttk.Treeview(review_window, columns=review_columns, show="headings", height=20)

        for col in review_columns:
            review_tree.heading(col, text=col)
            review_tree.column(col, width=200)

        for item in review_items:
            status_names = {0: "不会", 1: "模糊", 2: "已学会"}
            tags_display = ", ".join(item["tags"]) if item["tags"] else "无标签"
            review_tree.insert("", "end", values=(
                item["title"], tags_display, status_names.get(item["status"], "未知")
            ))

        review_tree.pack(fill="both", expand=True, padx=20, pady=10)

    def search_knowledge(self):
        """搜索知识"""
        keyword = self.search_entry.get().strip()
        if not keyword:
            messagebox.showwarning("警告", "请输入搜索关键词")
            return

        # 清空搜索结果
        for item in self.search_tree.get_children():
            self.search_tree.delete(item)

        # 执行搜索
        results = self.db.search_items(keyword)

        # 显示结果
        for item in results:
            # 查找匹配的内容片段
            match_content = ""
            if keyword.lower() in item["title"].lower():
                match_content = item["title"]
            elif keyword.lower() in item["content"].lower():
                # 显示匹配内容的前后文
                content = item["content"]
                index = content.lower().find(keyword.lower())
                start = max(0, index - 20)
                end = min(len(content), index + len(keyword) + 20)
                match_content = "..." + content[start:end] + "..."
            elif keyword.lower() in item["other_info"].lower():
                match_content = item["other_info"][:50] + "..."
            else:
                # 可能匹配的是标签
                for tag in item["tags"]:
                    if keyword.lower() in tag.lower():
                        match_content = f"标签: {tag}"
                        break

            tags_display = ", ".join(item["tags"]) if item["tags"] else "无标签"
            self.search_tree.insert("", "end", values=(
                item["id"], tags_display, item["title"], match_content
            ))

        if not results:
            messagebox.showinfo("搜索结果", "未找到匹配的内容")

    def show_search_item_detail(self, event):
        """显示搜索结果详情"""
        selection = self.search_tree.selection()
        if not selection:
            return

        item_data = self.search_tree.item(selection[0])["values"]
        item_id = item_data[0]

        # 获取完整信息
        item_detail = self.db.get_item_by_id(item_id)

        if item_detail:
            self.show_detail_window(item_detail)

    def delete_selected_item(self):
        """删除选中的知识条目"""
        selection = self.items_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的项目")
            return

        item_data = self.items_tree.item(selection[0])["values"]

        # 根据显示的列确定ID和标题的位置
        item_id = None
        item_title = "未知项目"

        col_index = 0
        if self.show_id_var.get():
            item_id = item_data[col_index]
            col_index += 1
        if self.show_tags_var.get():
            col_index += 1
        if self.show_title_var.get():
            item_title = item_data[col_index]

        # 如果没有显示ID列，需要通过其他方式获取ID
        if item_id is None:
            # 通过标题查找ID（这种情况下需要确保标题唯一）
            all_items = self.db.get_knowledge_items()
            for item in all_items:
                if item["title"] == item_title:
                    item_id = item["id"]
                    break

        if item_id is None:
            messagebox.showerror("错误", "无法确定要删除的项目ID")
            return

        # 确认删除
        result = messagebox.askyesno("确认删除",
                                   f"确定要删除项目 '{item_title}' 吗？\n\n此操作不可撤销！")
        if result:
            try:
                success = self.db.delete_knowledge_item(item_id)
                if success:
                    # 删除成功后自动重排ID
                    self.db.reorder_ids()
                    messagebox.showinfo("成功", "项目删除成功！ID已自动重新排列。")
                    self.refresh_items_list()
                else:
                    messagebox.showerror("错误", "删除失败：项目不存在")
            except Exception as e:
                messagebox.showerror("错误", f"删除失败：{str(e)}")

    def delete_item_from_detail(self, item_id, detail_window):
        """从详情窗口删除项目"""
        # 获取项目信息用于确认
        item = self.db.get_item_by_id(item_id)
        if not item:
            messagebox.showerror("错误", "项目不存在")
            return

        # 确认删除
        result = messagebox.askyesno("确认删除",
                                   f"确定要删除项目 '{item['title']}' 吗？\n\n此操作不可撤销！")
        if result:
            try:
                success = self.db.delete_knowledge_item(item_id)
                if success:
                    # 删除成功后自动重排ID
                    self.db.reorder_ids()
                    messagebox.showinfo("成功", "项目删除成功！ID已自动重新排列。")
                    detail_window.destroy()  # 关闭详情窗口
                    self.refresh_items_list()  # 刷新列表
                else:
                    messagebox.showerror("错误", "删除失败：项目不存在")
            except Exception as e:
                messagebox.showerror("错误", f"删除失败：{str(e)}")

    def export_to_excel(self):
        """导出数据到Excel文件"""
        try:
            # 获取当前显示的数据（考虑筛选条件）
            if hasattr(self, 'multi_filter_tags') and self.multi_filter_tags:
                # 如果有筛选条件，导出筛选后的数据
                items = self.get_items_by_multi_tags(self.multi_filter_tags,
                                                   self.current_sort_column,
                                                   not self.sort_ascending)
                filter_info = f"筛选条件: {', '.join(self.multi_filter_tags)}"
            else:
                # 导出全部数据
                items = self.db.get_knowledge_items(None,
                                                  self.current_sort_column,
                                                  not self.sort_ascending)
                filter_info = "全部数据"

            if not items:
                messagebox.showwarning("警告", "没有数据可以导出")
                return

            # 让用户选择保存位置
            file_path = filedialog.asksaveasfilename(
                title="保存Excel文件",
                defaultextension=".xlsx",
                filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")],
                initialname=f"知识库导出_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if not file_path:
                return  # 用户取消了保存

            # 准备导出数据
            export_data = []
            for item in items:
                # 格式化创建时间
                created_time = datetime.fromisoformat(item["created_at"]).strftime("%Y-%m-%d %H:%M:%S")

                # 处理标签
                tags_str = ", ".join(item["tags"]) if item["tags"] else "无标签"

                export_data.append({
                    "ID": item["id"],
                    "标签": tags_str,
                    "题目": item["title"],
                    "内容": item["content"],
                    "其他信息": item["other_info"] if item["other_info"] else "",
                    "创建时间": created_time
                })

            # 创建DataFrame
            df = pd.DataFrame(export_data)

            # 使用ExcelWriter来更好地控制格式
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 写入主数据
                df.to_excel(writer, sheet_name='知识库数据', index=False)

                # 获取工作表对象以进行格式设置
                try:
                    worksheet = writer.sheets['知识库数据']

                    # 调整列宽
                    column_widths = {
                        'A': 8,   # ID
                        'B': 20,  # 标签
                        'C': 30,  # 题目
                        'D': 50,  # 内容
                        'E': 30,  # 其他信息
                        'F': 20   # 创建时间
                    }

                    for col, width in column_widths.items():
                        worksheet.column_dimensions[col].width = width

                    # 设置内容列自动换行
                    try:
                        from openpyxl.styles import Alignment
                        for row in worksheet.iter_rows(min_row=2, max_row=len(df)+1, min_col=4, max_col=5):
                            for cell in row:
                                cell.alignment = Alignment(wrap_text=True, vertical='top')
                    except:
                        pass  # 如果格式设置失败，跳过但继续导出
                except:
                    pass  # 如果格式设置失败，跳过但继续导出

                # 创建统计信息工作表
                stats_data = {
                    "统计项目": ["总记录数", "导出时间", "导出条件", "标签总数"],
                    "数值": [
                        len(items),
                        datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        filter_info,
                        len(self.db.get_tags())
                    ]
                }

                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)

                # 调整统计信息工作表的列宽
                try:
                    stats_worksheet = writer.sheets['统计信息']
                    stats_worksheet.column_dimensions['A'].width = 15
                    stats_worksheet.column_dimensions['B'].width = 30
                except:
                    pass

                # 如果有标签数据，创建标签统计工作表
                tags = self.db.get_tags()
                if tags:
                    # 统计每个标签的使用次数
                    tag_counts = {}
                    for item in self.db.get_knowledge_items():
                        for tag in item["tags"]:
                            tag_counts[tag] = tag_counts.get(tag, 0) + 1

                    tag_stats_data = []
                    for tag in tags:
                        tag_name = tag["name"]
                        count = tag_counts.get(tag_name, 0)
                        tag_stats_data.append({
                            "标签名称": tag_name,
                            "使用次数": count
                        })

                    # 按使用次数排序
                    tag_stats_data.sort(key=lambda x: x["使用次数"], reverse=True)

                    tag_stats_df = pd.DataFrame(tag_stats_data)
                    tag_stats_df.to_excel(writer, sheet_name='标签统计', index=False)

                    # 调整标签统计工作表的列宽
                    try:
                        tag_stats_worksheet = writer.sheets['标签统计']
                        tag_stats_worksheet.column_dimensions['A'].width = 20
                        tag_stats_worksheet.column_dimensions['B'].width = 15
                    except:
                        pass

            messagebox.showinfo("导出成功",
                              f"数据已成功导出到:\n{file_path}\n\n"
                              f"导出记录数: {len(items)}\n"
                              f"导出条件: {filter_info}")

        except ImportError:
            messagebox.showerror("错误",
                               "缺少必要的库文件。请先安装依赖:\n"
                               "pip install pandas openpyxl")
        except Exception as e:
            messagebox.showerror("导出失败", f"导出过程中发生错误:\n{str(e)}")

    def import_from_excel(self):
        """从Excel文件导入数据"""
        try:
            # 让用户选择要导入的Excel文件
            file_path = filedialog.askopenfilename(
                title="选择Excel文件",
                filetypes=[("Excel文件", "*.xlsx"), ("Excel文件", "*.xls"), ("所有文件", "*.*")]
            )

            if not file_path:
                return  # 用户取消了选择

            # 读取Excel文件
            try:
                # 尝试读取"知识库数据"工作表，如果不存在则读取第一个工作表
                try:
                    df = pd.read_excel(file_path, sheet_name='知识库数据')
                except:
                    df = pd.read_excel(file_path, sheet_name=0)  # 读取第一个工作表

                if df.empty:
                    messagebox.showwarning("警告", "Excel文件中没有数据")
                    return

                # 检查必要的列是否存在
                required_columns = ['题目', '内容']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    messagebox.showerror("错误",
                                       f"Excel文件缺少必要的列: {', '.join(missing_columns)}\n\n"
                                       f"必须包含的列: {', '.join(required_columns)}\n"
                                       f"可选的列: 标签, 其他信息")
                    return

                # 显示导入预览和确认对话框
                if not self.show_import_preview(df, file_path):
                    return

                # 开始导入数据
                imported_count = 0
                skipped_count = 0
                error_count = 0

                for index, row in df.iterrows():
                    try:
                        # 获取基本信息
                        title = str(row['题目']).strip() if pd.notna(row['题目']) else ""
                        content = str(row['内容']).strip() if pd.notna(row['内容']) else ""

                        if not title or not content:
                            skipped_count += 1
                            continue

                        # 处理标签
                        tags = []
                        if '标签' in df.columns and pd.notna(row['标签']):
                            tags_str = str(row['标签']).strip()
                            if tags_str and tags_str != "无标签":
                                # 支持多种分隔符
                                for sep in [',', '，', ';', '；', '|']:
                                    if sep in tags_str:
                                        tags = [tag.strip() for tag in tags_str.split(sep) if tag.strip()]
                                        break
                                else:
                                    tags = [tags_str]

                        # 如果没有标签，使用默认标签
                        if not tags:
                            tags = ["导入数据"]

                        # 处理其他信息
                        other_info = ""
                        if '其他信息' in df.columns and pd.notna(row['其他信息']):
                            other_info = str(row['其他信息']).strip()

                        # 检查是否已存在相同的题目
                        existing_items = self.db.get_knowledge_items()
                        title_exists = any(item['title'] == title for item in existing_items)

                        if title_exists:
                            skipped_count += 1
                            continue

                        # 添加到数据库
                        self.db.add_knowledge_item(tags, title, content, other_info)
                        imported_count += 1

                    except Exception as e:
                        error_count += 1
                        print(f"导入第{index+1}行时出错: {str(e)}")

                # 刷新界面
                self.refresh_tags()
                self.refresh_items_list()

                # 显示导入结果
                result_message = f"导入完成！\n\n"
                result_message += f"✅ 成功导入: {imported_count} 条\n"
                if skipped_count > 0:
                    result_message += f"⚠️ 跳过: {skipped_count} 条 (空数据或重复题目)\n"
                if error_count > 0:
                    result_message += f"❌ 错误: {error_count} 条\n"

                messagebox.showinfo("导入结果", result_message)

            except Exception as e:
                messagebox.showerror("读取失败", f"无法读取Excel文件:\n{str(e)}")

        except ImportError:
            messagebox.showerror("错误",
                               "缺少必要的库文件。请先安装依赖:\n"
                               "pip install pandas openpyxl")
        except Exception as e:
            messagebox.showerror("导入失败", f"导入过程中发生错误:\n{str(e)}")

    def show_import_preview(self, df, file_path):
        """显示导入预览对话框"""
        preview_window = ctk.CTkToplevel(self.root)
        preview_window.title("导入预览")
        preview_window.geometry("800x600")
        preview_window.transient(self.root)
        preview_window.grab_set()

        # 文件信息
        info_frame = ctk.CTkFrame(preview_window)
        info_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(info_frame, text=f"文件: {os.path.basename(file_path)}",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=5)
        ctk.CTkLabel(info_frame, text=f"总行数: {len(df)}",
                    font=ctk.CTkFont(size=12)).pack(anchor="w", padx=10)
        ctk.CTkLabel(info_frame, text=f"列: {', '.join(df.columns)}",
                    font=ctk.CTkFont(size=12)).pack(anchor="w", padx=10, pady=(0, 5))

        # 预览数据
        ctk.CTkLabel(preview_window, text="数据预览 (前5行):",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=20, pady=(10, 5))

        # 创建预览表格
        preview_frame = ctk.CTkFrame(preview_window)
        preview_frame.pack(fill="both", expand=True, padx=20, pady=5)

        # 显示列标题和前几行数据
        columns = list(df.columns)
        preview_tree = ttk.Treeview(preview_frame, columns=columns, show="headings", height=8)

        for col in columns:
            preview_tree.heading(col, text=col)
            preview_tree.column(col, width=150)

        # 显示前5行数据
        for index, row in df.head(5).iterrows():
            values = []
            for col in columns:
                value = str(row[col]) if pd.notna(row[col]) else ""
                # 限制显示长度
                if len(value) > 30:
                    value = value[:30] + "..."
                values.append(value)
            preview_tree.insert("", "end", values=values)

        preview_tree.pack(fill="both", expand=True, padx=10, pady=10)

        # 滚动条
        scrollbar = ttk.Scrollbar(preview_frame, orient="vertical", command=preview_tree.yview)
        preview_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="right", fill="y")

        # 按钮框架
        button_frame = ctk.CTkFrame(preview_window)
        button_frame.pack(fill="x", padx=20, pady=10)

        # 用于存储用户选择的变量
        user_choice = {"confirmed": False}

        def confirm_import():
            user_choice["confirmed"] = True
            preview_window.destroy()

        def cancel_import():
            user_choice["confirmed"] = False
            preview_window.destroy()

        ctk.CTkButton(button_frame, text="确认导入", command=confirm_import,
                     fg_color="green", hover_color="darkgreen").pack(side="right", padx=5)
        ctk.CTkButton(button_frame, text="取消", command=cancel_import,
                     fg_color="gray", hover_color="darkgray").pack(side="right", padx=5)

        # 等待用户选择
        preview_window.wait_window()
        return user_choice["confirmed"]

    def run(self):
        """运行应用"""
        self.root.mainloop()

if __name__ == "__main__":
    app = KnowledgeBaseApp()
    app.run()
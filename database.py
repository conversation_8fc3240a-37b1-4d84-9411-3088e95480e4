import sqlite3
import datetime
from typing import List, Dict, Optional, Tuple

class DatabaseManager:
    def __init__(self, db_path: str = "knowledge_base.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库表"""
        conn = sqlite3.connect(self.db_path)
        try:
            cursor = conn.cursor()

            # 创建标签表（原分类表）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tags (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建知识条目表（移除category_id外键）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS knowledge_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    content TEXT NOT NULL,
                    other_info TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建项目-标签关联表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS item_tags (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_id INTEGER NOT NULL,
                    tag_id INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (item_id) REFERENCES knowledge_items (id) ON DELETE CASCADE,
                    FOREIGN KEY (tag_id) REFERENCES tags (id) ON DELETE CASCADE,
                    UNIQUE(item_id, tag_id)
                )
            ''')

            # 创建学习记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS learning_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_id INTEGER,
                    status INTEGER DEFAULT 0,  -- 0:不会, 1:模糊, 2:已学会
                    last_review TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    next_review TIMESTAMP,
                    review_count INTEGER DEFAULT 0,
                    FOREIGN KEY (item_id) REFERENCES knowledge_items (id) ON DELETE CASCADE
                )
            ''')

            # 数据迁移：如果存在旧的categories表，迁移到tags表
            self._migrate_categories_to_tags(cursor)

            conn.commit()
        finally:
            conn.close()

    def _migrate_categories_to_tags(self, cursor):
        """迁移旧的分类数据到新的标签系统"""
        try:
            # 检查是否存在旧的categories表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='categories'")
            if cursor.fetchone():
                # 迁移categories到tags
                cursor.execute("INSERT OR IGNORE INTO tags (name, created_at) SELECT name, created_at FROM categories")

                # 检查knowledge_items表是否有category_id列
                cursor.execute("PRAGMA table_info(knowledge_items)")
                columns = [col[1] for col in cursor.fetchall()]

                if 'category_id' in columns:
                    # 迁移knowledge_items的category_id到item_tags关联表
                    cursor.execute('''
                        INSERT OR IGNORE INTO item_tags (item_id, tag_id)
                        SELECT ki.id, t.id
                        FROM knowledge_items ki
                        JOIN categories c ON ki.category_id = c.id
                        JOIN tags t ON c.name = t.name
                        WHERE ki.category_id IS NOT NULL
                    ''')

                # 删除knowledge_items表中的category_id列（SQLite不支持直接删除列，需要重建表）
                # 这里我们保留该列以保持向后兼容性，但不再使用
        except sqlite3.Error:
            # 如果迁移失败，继续执行（可能是新安装）
            pass
    
    def add_tag(self, name: str) -> int:
        """添加标签"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        try:
            cursor.execute("INSERT INTO tags (name) VALUES (?)", (name,))
            tag_id = cursor.lastrowid
            conn.commit()
            return tag_id
        except sqlite3.IntegrityError:
            # 标签已存在，返回现有ID
            cursor.execute("SELECT id FROM tags WHERE name = ?", (name,))
            result = cursor.fetchone()
            return result[0] if result else None
        finally:
            conn.close()

    def get_tags(self) -> List[Dict]:
        """获取所有标签"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id, name FROM tags ORDER BY name")
        tags = [{"id": row[0], "name": row[1]} for row in cursor.fetchall()]
        conn.close()
        return tags

    # 保持向后兼容性的别名方法
    def add_category(self, name: str) -> int:
        """添加分类（向后兼容）"""
        return self.add_tag(name)

    def get_categories(self) -> List[Dict]:
        """获取所有分类（向后兼容）"""
        return self.get_tags()
    
    def add_knowledge_item(self, tags: List[str], title: str, content: str, other_info: str = "") -> int:
        """添加知识条目（支持多标签）"""
        conn = sqlite3.connect(self.db_path)
        try:
            cursor = conn.cursor()

            # 插入知识条目（显式设置创建时间）
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cursor.execute('''
                INSERT INTO knowledge_items (title, content, other_info, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (title, content, other_info, current_time, current_time))

            item_id = cursor.lastrowid

            # 添加标签关联（在同一个连接中处理）
            for tag_name in tags:
                # 先尝试插入标签
                try:
                    cursor.execute("INSERT INTO tags (name) VALUES (?)", (tag_name,))
                    tag_id = cursor.lastrowid
                except sqlite3.IntegrityError:
                    # 标签已存在，获取现有ID
                    cursor.execute("SELECT id FROM tags WHERE name = ?", (tag_name,))
                    result = cursor.fetchone()
                    tag_id = result[0] if result else None

                if tag_id:
                    cursor.execute('''
                        INSERT OR IGNORE INTO item_tags (item_id, tag_id)
                        VALUES (?, ?)
                    ''', (item_id, tag_id))

            # 创建学习记录
            next_review = datetime.datetime.now() + datetime.timedelta(days=1)
            cursor.execute('''
                INSERT INTO learning_records (item_id, next_review)
                VALUES (?, ?)
            ''', (item_id, next_review))

            conn.commit()
            return item_id
        finally:
            conn.close()

    # 保持向后兼容性的方法
    def add_knowledge_item_legacy(self, category_name: str, title: str, content: str, other_info: str = "") -> int:
        """添加知识条目（向后兼容单分类）"""
        return self.add_knowledge_item([category_name], title, content, other_info)
    
    def get_knowledge_items(self, tag_id: Optional[int] = None, order_by: str = "created_at", desc: bool = True) -> List[Dict]:
        """获取知识条目（支持多标签）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 确定排序方向
        order_direction = "DESC" if desc else "ASC"

        if tag_id:
            # 获取包含指定标签的项目
            if order_by == "tags":
                # 按标签排序时，需要特殊处理
                query = '''
                    SELECT DISTINCT ki.id, ki.title, ki.content, ki.other_info, ki.created_at
                    FROM knowledge_items ki
                    JOIN item_tags it ON ki.id = it.item_id
                    JOIN tags t ON it.tag_id = t.id
                    WHERE it.tag_id = ?
                    ORDER BY t.name {}
                '''.format(order_direction)
            else:
                query = '''
                    SELECT DISTINCT ki.id, ki.title, ki.content, ki.other_info, ki.created_at
                    FROM knowledge_items ki
                    JOIN item_tags it ON ki.id = it.item_id
                    WHERE it.tag_id = ?
                    ORDER BY ki.{} {}
                '''.format(order_by, order_direction)
            cursor.execute(query, (tag_id,))
        else:
            # 获取所有项目
            if order_by == "tags":
                # 按标签排序时，需要特殊处理 - 按第一个标签排序
                query = '''
                    SELECT ki.id, ki.title, ki.content, ki.other_info, ki.created_at
                    FROM knowledge_items ki
                    LEFT JOIN (
                        SELECT it.item_id, MIN(t.name) as first_tag
                        FROM item_tags it
                        JOIN tags t ON it.tag_id = t.id
                        GROUP BY it.item_id
                    ) first_tags ON ki.id = first_tags.item_id
                    ORDER BY COALESCE(first_tags.first_tag, 'zzz') {}
                '''.format(order_direction)
            else:
                query = '''
                    SELECT ki.id, ki.title, ki.content, ki.other_info, ki.created_at
                    FROM knowledge_items ki
                    ORDER BY ki.{} {}
                '''.format(order_by, order_direction)
            cursor.execute(query)

        items = []
        for row in cursor.fetchall():
            item_id = row[0]
            # 获取该项目的所有标签，按名称排序
            tags = self.get_item_tags(item_id)
            tag_names = sorted([tag["name"] for tag in tags])  # 排序标签名称

            items.append({
                "id": item_id,
                "title": row[1],
                "content": row[2],
                "other_info": row[3],
                "created_at": row[4],
                "tags": tag_names,
                "category": tag_names[0] if tag_names else ""  # 向后兼容
            })

        # 如果按标签排序，需要进一步排序
        if order_by == "tags" and not tag_id:
            items.sort(key=lambda x: (
                x["tags"][0] if x["tags"] else "zzz",  # 第一个标签
                x["tags"][1] if len(x["tags"]) > 1 else "zzz",  # 第二个标签
                x["title"]  # 标题作为最后的排序依据
            ), reverse=desc)

        conn.close()
        return items

    def delete_tag(self, tag_id: int):
        """删除标签及其所有关联"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 删除标签与项目的关联
            cursor.execute("DELETE FROM item_tags WHERE tag_id = ?", (tag_id,))

            # 删除标签本身
            cursor.execute("DELETE FROM tags WHERE id = ?", (tag_id,))

            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()

    def reorder_ids(self):
        """重新排列ID，按创建时间顺序"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 获取所有项目，按创建时间排序
            cursor.execute('''
                SELECT id, title, content, other_info, created_at, updated_at
                FROM knowledge_items
                ORDER BY created_at ASC
            ''')
            items = cursor.fetchall()

            # 获取所有项目的标签关联
            item_tags_map = {}
            for item in items:
                item_id = item[0]
                cursor.execute('''
                    SELECT tag_id FROM item_tags WHERE item_id = ?
                ''', (item_id,))
                tag_ids = [row[0] for row in cursor.fetchall()]
                item_tags_map[item_id] = tag_ids

            # 删除所有现有数据
            cursor.execute('DELETE FROM item_tags')
            cursor.execute('DELETE FROM knowledge_items')

            # 重置自增序列
            cursor.execute('DELETE FROM sqlite_sequence WHERE name="knowledge_items"')

            # 重新插入数据，ID会从1开始重新分配
            for item in items:
                old_id = item[0]
                cursor.execute('''
                    INSERT INTO knowledge_items (title, content, other_info, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                ''', (item[1], item[2], item[3], item[4], item[5]))

                new_id = cursor.lastrowid

                # 重新建立标签关联
                for tag_id in item_tags_map[old_id]:
                    cursor.execute('''
                        INSERT INTO item_tags (item_id, tag_id)
                        VALUES (?, ?)
                    ''', (new_id, tag_id))

            conn.commit()

        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()

    def get_item_tags(self, item_id: int) -> List[Dict]:
        """获取指定项目的所有标签"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT t.id, t.name
            FROM tags t
            JOIN item_tags it ON t.id = it.tag_id
            WHERE it.item_id = ?
            ORDER BY t.name
        ''', (item_id,))

        tags = [{"id": row[0], "name": row[1]} for row in cursor.fetchall()]
        conn.close()
        return tags
    
    def search_items(self, keyword: str) -> List[Dict]:
        """搜索知识条目（支持多标签）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 搜索标题、内容、其他信息和标签名称
        query = '''
            SELECT DISTINCT ki.id, ki.title, ki.content, ki.other_info, ki.created_at
            FROM knowledge_items ki
            LEFT JOIN item_tags it ON ki.id = it.item_id
            LEFT JOIN tags t ON it.tag_id = t.id
            WHERE ki.title LIKE ? OR ki.content LIKE ? OR ki.other_info LIKE ? OR t.name LIKE ?
            ORDER BY ki.created_at DESC
        '''
        search_term = f"%{keyword}%"
        cursor.execute(query, (search_term, search_term, search_term, search_term))

        items = []
        for row in cursor.fetchall():
            item_id = row[0]
            # 获取该项目的所有标签
            tags = self.get_item_tags(item_id)
            tag_names = [tag["name"] for tag in tags]

            items.append({
                "id": item_id,
                "title": row[1],
                "content": row[2],
                "other_info": row[3],
                "created_at": row[4],
                "tags": tag_names,
                "category": tag_names[0] if tag_names else ""  # 向后兼容
            })

        conn.close()
        return items
    
    def get_random_item(self) -> Optional[Dict]:
        """获取随机知识条目（支持多标签）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, title, content, other_info
            FROM knowledge_items
            ORDER BY RANDOM()
            LIMIT 1
        ''')

        row = cursor.fetchone()
        conn.close()

        if row:
            item_id = row[0]
            # 获取该项目的所有标签
            tags = self.get_item_tags(item_id)
            tag_names = [tag["name"] for tag in tags]

            return {
                "id": item_id,
                "title": row[1],
                "content": row[2],
                "other_info": row[3],
                "tags": tag_names,
                "category": tag_names[0] if tag_names else ""  # 向后兼容
            }
        return None
    
    def update_learning_status(self, item_id: int, status: int):
        """更新学习状态"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 计算下次复习时间（艾宾浩斯曲线）
        intervals = {0: 1, 1: 3, 2: 7}  # 不会:1天, 模糊:3天, 已学会:7天
        next_review = datetime.datetime.now() + datetime.timedelta(days=intervals.get(status, 1))
        
        cursor.execute('''
            UPDATE learning_records 
            SET status = ?, last_review = CURRENT_TIMESTAMP, next_review = ?, review_count = review_count + 1
            WHERE item_id = ?
        ''', (status, next_review, item_id))
        
        conn.commit()
        conn.close()
    
    def get_items_for_review(self) -> List[Dict]:
        """获取需要复习的条目（支持多标签）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT ki.id, ki.title, ki.content, ki.other_info, lr.status
            FROM knowledge_items ki
            JOIN learning_records lr ON ki.id = lr.item_id
            WHERE lr.next_review <= CURRENT_TIMESTAMP
            ORDER BY lr.next_review
        ''')

        items = []
        for row in cursor.fetchall():
            item_id = row[0]
            # 获取该项目的所有标签
            tags = self.get_item_tags(item_id)
            tag_names = [tag["name"] for tag in tags]

            items.append({
                "id": item_id,
                "title": row[1],
                "content": row[2],
                "other_info": row[3],
                "tags": tag_names,
                "category": tag_names[0] if tag_names else "",  # 向后兼容
                "status": row[4]
            })

        conn.close()
        return items

    def delete_knowledge_item(self, item_id: int) -> bool:
        """删除知识条目及其相关数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 删除学习记录
            cursor.execute("DELETE FROM learning_records WHERE item_id = ?", (item_id,))

            # 删除标签关联
            cursor.execute("DELETE FROM item_tags WHERE item_id = ?", (item_id,))

            # 删除知识条目
            cursor.execute("DELETE FROM knowledge_items WHERE id = ?", (item_id,))

            # 检查是否成功删除
            if cursor.rowcount > 0:
                conn.commit()
                return True
            else:
                return False

        except sqlite3.Error as e:
            conn.rollback()
            raise e
        finally:
            conn.close()



    def get_item_by_id(self, item_id: int) -> Optional[Dict]:
        """根据ID获取单个知识条目"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, title, content, other_info, created_at, updated_at
            FROM knowledge_items
            WHERE id = ?
        ''', (item_id,))

        row = cursor.fetchone()
        conn.close()

        if row:
            # 获取该项目的所有标签
            tags = self.get_item_tags(item_id)
            tag_names = [tag["name"] for tag in tags]

            return {
                "id": row[0],
                "title": row[1],
                "content": row[2],
                "other_info": row[3],
                "created_at": row[4],
                "updated_at": row[5],
                "tags": tag_names,
                "category": tag_names[0] if tag_names else ""  # 向后兼容
            }
        return None

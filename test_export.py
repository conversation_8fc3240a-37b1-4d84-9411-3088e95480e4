#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导出Excel功能的脚本
"""

import pandas as pd
from database import DatabaseManager
from datetime import datetime
import os

def test_export_functionality():
    """测试导出功能"""
    print("开始测试导出Excel功能...")
    
    # 初始化数据库
    db = DatabaseManager()
    
    # 添加一些测试数据（如果数据库为空）
    items = db.get_knowledge_items()
    if not items:
        print("数据库为空，添加测试数据...")
        test_data = [
            (["Python", "编程"], "Python基础语法", "Python是一种高级编程语言", "适合初学者"),
            (["数学", "算法"], "快速排序算法", "快速排序是一种高效的排序算法", "时间复杂度O(nlogn)"),
            (["英语", "学习"], "英语语法", "英语语法的基本规则", "包括时态、语态等"),
        ]
        
        for tags, title, content, other in test_data:
            db.add_knowledge_item(tags, title, content, other)
        
        items = db.get_knowledge_items()
        print(f"已添加 {len(items)} 条测试数据")
    else:
        print(f"数据库中已有 {len(items)} 条数据")
    
    # 测试导出功能
    try:
        # 准备导出数据
        export_data = []
        for item in items:
            created_time = datetime.fromisoformat(item["created_at"]).strftime("%Y-%m-%d %H:%M:%S")
            tags_str = ", ".join(item["tags"]) if item["tags"] else "无标签"
            
            export_data.append({
                "ID": item["id"],
                "标签": tags_str,
                "题目": item["title"],
                "内容": item["content"],
                "其他信息": item["other_info"] if item["other_info"] else "",
                "创建时间": created_time
            })

        # 创建DataFrame
        df = pd.DataFrame(export_data)
        
        # 导出到Excel文件
        output_file = f"测试导出_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 写入主数据
            df.to_excel(writer, sheet_name='知识库数据', index=False)
            
            # 获取工作表对象
            worksheet = writer.sheets['知识库数据']
            
            # 调整列宽
            column_widths = {
                'A': 8,   # ID
                'B': 20,  # 标签
                'C': 30,  # 题目
                'D': 50,  # 内容
                'E': 30,  # 其他信息
                'F': 20   # 创建时间
            }
            
            for col, width in column_widths.items():
                worksheet.column_dimensions[col].width = width
            
            # 设置内容列自动换行
            from openpyxl.styles import Alignment
            for row in worksheet.iter_rows(min_row=2, max_row=len(df)+1, min_col=4, max_col=5):
                for cell in row:
                    cell.alignment = Alignment(wrap_text=True, vertical='top')

            # 创建统计信息工作表
            stats_data = {
                "统计项目": ["总记录数", "导出时间", "导出条件", "标签总数"],
                "数值": [
                    len(items),
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "全部数据",
                    len(db.get_tags())
                ]
            }
            
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='统计信息', index=False)
            
            # 调整统计信息工作表的列宽
            stats_worksheet = writer.sheets['统计信息']
            stats_worksheet.column_dimensions['A'].width = 15
            stats_worksheet.column_dimensions['B'].width = 30

            # 标签统计工作表
            tags = db.get_tags()
            if tags:
                tag_counts = {}
                for item in db.get_knowledge_items():
                    for tag in item["tags"]:
                        tag_counts[tag] = tag_counts.get(tag, 0) + 1
                
                tag_stats_data = []
                for tag in tags:
                    tag_name = tag["name"]
                    count = tag_counts.get(tag_name, 0)
                    tag_stats_data.append({
                        "标签名称": tag_name,
                        "使用次数": count
                    })
                
                tag_stats_data.sort(key=lambda x: x["使用次数"], reverse=True)
                
                tag_stats_df = pd.DataFrame(tag_stats_data)
                tag_stats_df.to_excel(writer, sheet_name='标签统计', index=False)
                
                tag_stats_worksheet = writer.sheets['标签统计']
                tag_stats_worksheet.column_dimensions['A'].width = 20
                tag_stats_worksheet.column_dimensions['B'].width = 15

        print(f"✅ 导出成功！文件保存为: {output_file}")
        print(f"📊 导出记录数: {len(items)}")
        print(f"📁 文件大小: {os.path.getsize(output_file)} 字节")
        
        return True
        
    except Exception as e:
        print(f"❌ 导出失败: {str(e)}")
        return False

if __name__ == "__main__":
    test_export_functionality()

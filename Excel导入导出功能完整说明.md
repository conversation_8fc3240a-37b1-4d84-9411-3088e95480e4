# Excel导入导出功能完整说明

## 功能概述

智能知识管理系统现已支持完整的Excel导入导出功能，让您可以：
- 📤 **导出数据**：将知识库数据导出为Excel格式，便于备份和分享
- 📥 **导入数据**：从Excel文件批量导入知识数据，快速建立知识库

## 🔄 导出功能

### 功能特性
- **智能筛选导出**：支持导出当前筛选条件下的数据
- **全量导出**：支持导出全部知识库数据
- **排序保持**：导出数据保持当前的排序方式
- **多工作表**：自动生成数据、统计、标签统计三个工作表

### 使用方法
1. 启动程序，切换到"知识列表"选项卡
2. 可选择设置筛选条件（导出特定标签的数据）
3. 点击绿色的"导出Excel"按钮
4. 选择保存位置和文件名
5. 确认导出

### 导出文件结构
- **知识库数据**：主数据工作表，包含ID、标签、题目、内容、其他信息、创建时间
- **统计信息**：导出统计，包含记录数、导出时间、筛选条件等
- **标签统计**：各标签使用频率统计

## 📥 导入功能

### 功能特性
- **智能列识别**：自动识别Excel文件中的数据列
- **预览确认**：导入前显示数据预览，确保数据正确
- **重复检测**：自动跳过重复题目的数据
- **错误处理**：详细的错误报告和跳过统计
- **多格式支持**：支持.xlsx和.xls格式

### 使用方法
1. 准备Excel文件（参考下面的格式要求）
2. 启动程序，切换到"知识列表"选项卡
3. 点击蓝色的"导入Excel"按钮
4. 选择要导入的Excel文件
5. 在预览界面确认数据无误
6. 点击"确认导入"开始导入

### Excel文件格式要求

#### 必需列
- **题目**：知识条目的标题，不能为空
- **内容**：知识条目的详细内容，不能为空

#### 可选列
- **标签**：多个标签用逗号分隔，如"Python,编程,基础"
- **其他信息**：补充信息，可以为空

#### 示例格式
| 标签 | 题目 | 内容 | 其他信息 |
|------|------|------|----------|
| Python,编程 | Python基础语法 | Python是一种高级编程语言... | 适合初学者 |
| 数学,算法 | 快速排序 | 快速排序是一种高效的排序算法... | 时间复杂度O(nlogn) |

### 导入规则
1. **重复检测**：相同题目的数据会被跳过
2. **标签处理**：支持多种分隔符（逗号、分号等）
3. **空数据跳过**：缺少题目或内容的行会被跳过
4. **默认标签**：没有标签的数据会自动添加"导入数据"标签

## 🛠️ 安装和配置

### 依赖库
```bash
pip install pandas openpyxl
```

### 系统要求
- Python 3.7+
- pandas >= 1.5.0
- openpyxl >= 3.0.0

## 📋 使用示例

### 创建测试导入文件
系统提供了测试文件生成脚本：
```bash
python 创建测试导入文件.py
```

这会创建包含示例数据的Excel文件，您可以用它来测试导入功能。

### 导出现有数据
1. 在知识列表中查看当前数据
2. 可选择添加筛选标签
3. 点击"导出Excel"保存数据

### 批量导入数据
1. 准备符合格式的Excel文件
2. 使用"导入Excel"功能
3. 预览确认后导入

## ⚠️ 注意事项

### 导出注意事项
- 导出文件默认保存为.xlsx格式
- 大量数据导出可能需要一些时间
- 确保有足够的磁盘空间

### 导入注意事项
- Excel文件必须包含"题目"和"内容"列
- 重复题目的数据会被自动跳过
- 建议先用小量数据测试导入功能
- 导入前建议备份现有数据

### 数据安全
- 导入操作不可撤销，建议先备份
- 大量导入前建议先测试小批量数据
- 定期导出数据作为备份

## 🔧 故障排除

### 常见问题

**Q: 导出时提示"缺少必要的库文件"**
A: 运行 `pip install pandas openpyxl` 安装依赖

**Q: 导入时提示"Excel文件缺少必要的列"**
A: 确保Excel文件包含"题目"和"内容"列

**Q: 导入的数据没有标签**
A: 系统会自动为没有标签的数据添加"导入数据"标签

**Q: 部分数据导入失败**
A: 检查导入结果报告，通常是因为数据格式问题或重复题目

**Q: 导出的Excel文件无法打开**
A: 确保安装了Excel或其他支持.xlsx格式的软件

### 错误代码说明
- **成功导入**：数据正常添加到知识库
- **跳过**：重复题目或空数据
- **错误**：数据格式问题或系统错误

## 📈 最佳实践

### 数据管理
1. **定期备份**：每周导出一次完整数据
2. **分类导出**：按标签导出不同主题的数据
3. **版本控制**：在文件名中包含日期

### 批量导入
1. **小批量测试**：先导入少量数据测试
2. **数据清理**：导入前检查数据格式
3. **标签规范**：使用统一的标签命名规则

### 数据质量
1. **避免重复**：导入前检查是否有重复内容
2. **完整信息**：确保题目和内容都有意义
3. **标签一致**：使用一致的标签体系

## 🎯 高级用法

### 自定义导入格式
如果您的Excel文件格式特殊，可以：
1. 调整列名以匹配系统要求
2. 使用Excel的数据处理功能预处理数据
3. 分批导入不同类型的数据

### 数据迁移
从其他系统迁移数据：
1. 将原系统数据导出为Excel
2. 调整格式以符合导入要求
3. 使用导入功能迁移到新系统

---

**提示**：首次使用建议先用测试数据熟悉导入导出流程，确保数据安全。

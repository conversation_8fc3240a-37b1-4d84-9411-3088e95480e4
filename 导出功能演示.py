#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel导出功能演示脚本
展示如何使用新增的导出功能
"""

from database import DatabaseManager
import pandas as pd
from datetime import datetime
import os

def demo_export_features():
    """演示导出功能的各种用法"""
    print("=" * 60)
    print("🎯 智能知识管理系统 - Excel导出功能演示")
    print("=" * 60)
    
    # 初始化数据库
    db = DatabaseManager()
    
    # 检查数据库状态
    items = db.get_knowledge_items()
    tags = db.get_tags()
    
    print(f"📊 当前数据库状态：")
    print(f"   - 知识条目数量：{len(items)}")
    print(f"   - 标签数量：{len(tags)}")
    print()
    
    if not items:
        print("⚠️  数据库为空，正在添加演示数据...")
        demo_data = [
            (["Python", "编程", "基础"], "Python变量和数据类型", 
             "Python中的基本数据类型包括int、float、str、bool等", 
             "变量命名要遵循PEP8规范"),
            (["Python", "编程", "进阶"], "Python函数定义", 
             "使用def关键字定义函数，支持参数默认值和可变参数", 
             "函数是代码复用的基本单位"),
            (["数学", "算法"], "冒泡排序算法", 
             "冒泡排序通过相邻元素比较和交换来排序", 
             "时间复杂度O(n²)，适合小数据集"),
            (["数学", "算法", "高级"], "快速排序算法", 
             "快速排序使用分治思想，选择基准元素进行分区", 
             "平均时间复杂度O(nlogn)"),
            (["英语", "语法"], "英语时态", 
             "英语有12种基本时态，包括一般、进行、完成、完成进行", 
             "掌握时态是英语学习的基础"),
            (["英语", "词汇"], "英语词根词缀", 
             "通过词根词缀可以快速扩展词汇量", 
             "常见词根如-spect(看)、-port(运输)等"),
        ]
        
        for tags_list, title, content, other in demo_data:
            db.add_knowledge_item(tags_list, title, content, other)
        
        items = db.get_knowledge_items()
        tags = db.get_tags()
        print(f"✅ 已添加 {len(demo_data)} 条演示数据")
        print()
    
    # 演示1：导出全部数据
    print("📤 演示1：导出全部数据")
    export_all_data(db, items)
    print()
    
    # 演示2：按标签筛选导出
    print("📤 演示2：按标签筛选导出")
    export_filtered_data(db)
    print()
    
    # 演示3：显示导出文件信息
    print("📁 演示3：查看导出文件")
    show_export_files()
    print()
    
    print("🎉 演示完成！")
    print("💡 提示：在GUI界面中，您可以通过以下步骤使用导出功能：")
    print("   1. 启动程序：python main.py")
    print("   2. 切换到'知识列表'选项卡")
    print("   3. 可选择添加筛选标签")
    print("   4. 点击绿色的'导出Excel'按钮")
    print("   5. 选择保存位置和文件名")

def export_all_data(db, items):
    """导出全部数据"""
    try:
        # 准备导出数据
        export_data = []
        for item in items:
            created_time = datetime.fromisoformat(item["created_at"]).strftime("%Y-%m-%d %H:%M:%S")
            tags_str = ", ".join(item["tags"]) if item["tags"] else "无标签"
            
            export_data.append({
                "ID": item["id"],
                "标签": tags_str,
                "题目": item["title"],
                "内容": item["content"],
                "其他信息": item["other_info"] if item["other_info"] else "",
                "创建时间": created_time
            })

        # 导出文件
        filename = f"全部数据导出_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 主数据工作表
            df = pd.DataFrame(export_data)
            df.to_excel(writer, sheet_name='知识库数据', index=False)
            
            # 统计信息工作表
            stats_data = {
                "统计项目": ["总记录数", "导出时间", "导出条件", "标签总数"],
                "数值": [
                    len(items),
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "全部数据",
                    len(db.get_tags())
                ]
            }
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='统计信息', index=False)
            
            # 标签统计工作表
            create_tag_stats_sheet(writer, db)
        
        print(f"   ✅ 成功导出全部 {len(items)} 条数据到：{filename}")
        
    except Exception as e:
        print(f"   ❌ 导出失败：{str(e)}")

def export_filtered_data(db):
    """按标签筛选导出数据"""
    try:
        # 筛选包含"Python"标签的数据
        filter_tag = "Python"
        all_items = db.get_knowledge_items()
        filtered_items = [item for item in all_items if filter_tag in item["tags"]]
        
        if not filtered_items:
            print(f"   ⚠️  没有找到包含'{filter_tag}'标签的数据")
            return
        
        # 准备导出数据
        export_data = []
        for item in filtered_items:
            created_time = datetime.fromisoformat(item["created_at"]).strftime("%Y-%m-%d %H:%M:%S")
            tags_str = ", ".join(item["tags"])
            
            export_data.append({
                "ID": item["id"],
                "标签": tags_str,
                "题目": item["title"],
                "内容": item["content"],
                "其他信息": item["other_info"] if item["other_info"] else "",
                "创建时间": created_time
            })

        # 导出文件
        filename = f"筛选导出_{filter_tag}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            df = pd.DataFrame(export_data)
            df.to_excel(writer, sheet_name='知识库数据', index=False)
            
            # 统计信息
            stats_data = {
                "统计项目": ["总记录数", "导出时间", "导出条件", "筛选标签"],
                "数值": [
                    len(filtered_items),
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    f"筛选条件: {filter_tag}",
                    filter_tag
                ]
            }
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='统计信息', index=False)
        
        print(f"   ✅ 成功导出 {len(filtered_items)} 条包含'{filter_tag}'标签的数据到：{filename}")
        
    except Exception as e:
        print(f"   ❌ 筛选导出失败：{str(e)}")

def create_tag_stats_sheet(writer, db):
    """创建标签统计工作表"""
    tags = db.get_tags()
    if tags:
        # 统计每个标签的使用次数
        tag_counts = {}
        for item in db.get_knowledge_items():
            for tag in item["tags"]:
                tag_counts[tag] = tag_counts.get(tag, 0) + 1
        
        tag_stats_data = []
        for tag in tags:
            tag_name = tag["name"]
            count = tag_counts.get(tag_name, 0)
            tag_stats_data.append({
                "标签名称": tag_name,
                "使用次数": count
            })
        
        # 按使用次数排序
        tag_stats_data.sort(key=lambda x: x["使用次数"], reverse=True)
        
        tag_stats_df = pd.DataFrame(tag_stats_data)
        tag_stats_df.to_excel(writer, sheet_name='标签统计', index=False)

def show_export_files():
    """显示当前目录下的导出文件"""
    export_files = [f for f in os.listdir('.') if f.endswith('.xlsx') and ('导出' in f or '测试' in f)]
    
    if export_files:
        print("   📁 当前目录下的导出文件：")
        for i, file in enumerate(export_files, 1):
            size = os.path.getsize(file)
            print(f"   {i}. {file} ({size} 字节)")
    else:
        print("   📁 当前目录下没有找到导出文件")

if __name__ == "__main__":
    demo_export_features()
